"""
Configuration settings for XAUUSD AI Trading System
"""
import os
from typing import Dict, List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # API Configuration
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_WORKERS: int = 1
    
    # Model Configuration
    MODEL_PATH: str = "models/xauusd_model.h5"
    MODEL_TYPE: str = "lstm"  # lstm, transformer, ensemble
    SEQUENCE_LENGTH: int = 60
    PREDICTION_HORIZON: int = 1
    
    # Data Configuration
    DATA_SOURCE: str = "yfinance"  # yfinance, ccxt, custom
    SYMBOL: str = "GC=F"  # Gold futures symbol for Yahoo Finance
    TIMEFRAMES: List[str] = ["15m", "1h", "4h"]
    LOOKBACK_DAYS: int = 365
    
    # Trading Configuration
    SIGNAL_THRESHOLD: float = 0.6
    CONFIDENCE_THRESHOLD: float = 0.7
    RISK_MANAGEMENT: bool = True
    MAX_SIGNALS_PER_DAY: int = 10
    
    # TradingView Webhook Configuration
    TRADINGVIEW_WEBHOOK_URL: str = ""
    WEBHOOK_SECRET: str = "your-secret-key"
    
    # Technical Indicators Configuration
    RSI_PERIOD: int = 14
    MACD_FAST: int = 12
    MACD_SLOW: int = 26
    MACD_SIGNAL: int = 9
    BB_PERIOD: int = 20
    BB_STD: float = 2.0
    ADX_PERIOD: int = 14
    
    # Timeframe-specific parameters
    TIMEFRAME_PARAMS: Dict = {
        "15m": {
            "rsi_period": 14,
            "bb_period": 20,
            "signal_threshold": 0.65,
            "confidence_threshold": 0.75
        },
        "1h": {
            "rsi_period": 14,
            "bb_period": 20,
            "signal_threshold": 0.6,
            "confidence_threshold": 0.7
        },
        "4h": {
            "rsi_period": 14,
            "bb_period": 20,
            "signal_threshold": 0.55,
            "confidence_threshold": 0.65
        }
    }
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/xauusd_ai.log"
    
    # Database Configuration (optional)
    DATABASE_URL: str = "sqlite:///xauusd_data.db"
    
    # External APIs
    ALPHA_VANTAGE_API_KEY: str = ""
    POLYGON_API_KEY: str = ""
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "allow"


# Global settings instance
settings = Settings()


# Model architectures configuration
MODEL_CONFIGS = {
    "lstm": {
        "layers": [
            {"type": "lstm", "units": 128, "return_sequences": True, "dropout": 0.2},
            {"type": "lstm", "units": 64, "return_sequences": True, "dropout": 0.2},
            {"type": "lstm", "units": 32, "return_sequences": False, "dropout": 0.2},
            {"type": "dense", "units": 25, "activation": "relu"},
            {"type": "dense", "units": 1, "activation": "sigmoid"}
        ],
        "optimizer": "adam",
        "loss": "binary_crossentropy",
        "metrics": ["accuracy"]
    },
    "transformer": {
        "d_model": 128,
        "num_heads": 8,
        "num_layers": 4,
        "dff": 512,
        "dropout_rate": 0.1,
        "optimizer": "adam",
        "loss": "binary_crossentropy",
        "metrics": ["accuracy"]
    }
}


# Feature engineering configuration
FEATURE_CONFIG = {
    "price_features": ["open", "high", "low", "close", "volume"],
    "technical_indicators": [
        "rsi", "macd", "macd_signal", "macd_histogram",
        "bb_upper", "bb_middle", "bb_lower", "bb_width",
        "adx", "di_plus", "di_minus",
        "sma_20", "sma_50", "ema_12", "ema_26",
        "stoch_k", "stoch_d", "atr"
    ],
    "derived_features": [
        "price_change", "volume_change", "volatility",
        "trend_strength", "momentum", "support_resistance"
    ]
}
